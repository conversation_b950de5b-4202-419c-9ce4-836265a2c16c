import React from 'react';
import { Users, Eye, Edit, Calendar } from 'lucide-react';

const recentPatients = [
  {
    id: 'PT001247',
    name: '<PERSON>',
    age: 45,
    gender: 'Male',
    department: 'Cardiology',
    status: 'Admitted',
    lastVisit: '2 hours ago',
    priority: 'High'
  },
  {
    id: 'PT001246',
    name: '<PERSON>',
    age: 32,
    gender: 'Female',
    department: 'Orthopedics',
    status: 'Outpatient',
    lastVisit: '1 day ago',
    priority: 'Medium'
  },
  {
    id: 'PT001245',
    name: '<PERSON>',
    age: 67,
    gender: 'Male',
    department: 'Neurology',
    status: 'Discharged',
    lastVisit: '3 days ago',
    priority: 'Low'
  },
  {
    id: 'PT001244',
    name: '<PERSON>',
    age: 28,
    gender: 'Female',
    department: 'Dermatology',
    status: 'Scheduled',
    lastVisit: '1 week ago',
    priority: 'Medium'
  }
];

export function RecentPatients() {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Admitted': return 'bg-blue-100 text-blue-800';
      case 'Outpatient': return 'bg-green-100 text-green-800';
      case 'Discharged': return 'bg-gray-100 text-gray-800';
      case 'Scheduled': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Recent Patients</h2>
        <Users size={20} className="text-gray-500" />
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Patient</th>
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Department</th>
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Status</th>
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Priority</th>
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Last Visit</th>
              <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Actions</th>
            </tr>
          </thead>
          <tbody>
            {recentPatients.map((patient) => (
              <tr key={patient.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div>
                    <div className="font-medium text-gray-900">{patient.name}</div>
                    <div className="text-sm text-gray-500">{patient.id} • {patient.age}y, {patient.gender}</div>
                  </div>
                </td>
                <td className="py-3 px-4 text-sm text-gray-900">{patient.department}</td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(patient.status)}`}>
                    {patient.status}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(patient.priority)}`}>
                    {patient.priority}
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-gray-500">{patient.lastVisit}</td>
                <td className="py-3 px-4">
                  <div className="flex space-x-2">
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Eye size={16} className="text-gray-500" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Edit size={16} className="text-gray-500" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Calendar size={16} className="text-gray-500" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}