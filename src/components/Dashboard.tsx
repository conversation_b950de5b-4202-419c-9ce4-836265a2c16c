import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Bed,
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  AlertCircle,
  Clock
} from 'lucide-react';
import { StatsCard } from './StatsCard';
import { RecentPatients } from './RecentPatients';
import { AppointmentSchedule } from './AppointmentSchedule';
import { QuickActions } from './QuickActions';
import { reportsAPI } from '../services/apiService';

export function Dashboard() {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleNavigate = (module: string) => {
    navigate(`/${module}`);
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await reportsAPI.getDashboard();
        setDashboardData(data.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data');
        // Set fallback data
        setDashboardData({
          patients: { total: 0, newThisMonth: 0 },
          appointments: { today: 0, scheduled: 0 },
          financial: { monthlyRevenue: 0, outstandingAmount: 0 },
          laboratory: { pendingTests: 0 },
          inventory: { lowStockItems: 0 }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <div className="text-sm text-gray-500">Loading...</div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleString()}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Patients"
          value={dashboardData?.patients?.total?.toLocaleString() || '0'}
          change={`+${dashboardData?.patients?.newThisMonth || 0} this month`}
          changeType="increase"
          icon={Users}
          color="blue"
        />
        <StatsCard
          title="Today's Appointments"
          value={dashboardData?.appointments?.today?.toString() || '0'}
          change={`${dashboardData?.appointments?.scheduled || 0} scheduled`}
          changeType="neutral"
          icon={Calendar}
          color="purple"
        />
        <StatsCard
          title="Monthly Revenue"
          value={`$${(dashboardData?.financial?.monthlyRevenue || 0).toLocaleString()}`}
          change={`$${(dashboardData?.financial?.outstandingAmount || 0).toLocaleString()} outstanding`}
          changeType="neutral"
          icon={DollarSign}
          color="emerald"
        />
        <StatsCard
          title="Pending Lab Tests"
          value={dashboardData?.laboratory?.pendingTests?.toString() || '0'}
          change={`${dashboardData?.inventory?.lowStockItems || 0} low stock items`}
          changeType="neutral"
          icon={Activity}
          color="orange"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - 2/3 width */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
              <Activity size={20} className="text-gray-500" />
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">New patient registered</p>
                  <p className="text-xs text-gray-500">John Doe - ID: PT001247</p>
                </div>
                <span className="text-xs text-gray-500">2 min ago</span>
              </div>
              <div className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Lab results ready</p>
                  <p className="text-xs text-gray-500">Patient ID: PT001203 - Blood Test</p>
                </div>
                <span className="text-xs text-gray-500">5 min ago</span>
              </div>
              <div className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Appointment scheduled</p>
                  <p className="text-xs text-gray-500">Dr. Smith - Tomorrow 10:00 AM</p>
                </div>
                <span className="text-xs text-gray-500">10 min ago</span>
              </div>
            </div>
          </div>

          {/* Recent Patients */}
          <RecentPatients />
        </div>

        {/* Right Column - 1/3 width */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <QuickActions onNavigate={handleNavigate} />

          {/* Today's Schedule */}
          <AppointmentSchedule />

          {/* Alerts */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Alerts</h2>
              <AlertCircle size={20} className="text-orange-500" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-red-900">Critical Patient</p>
                  <p className="text-xs text-red-700">Room 205 - Requires immediate attention</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-900">Low Inventory</p>
                  <p className="text-xs text-yellow-700">Paracetamol - 5 units remaining</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}