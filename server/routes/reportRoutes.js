import express from 'express';
import {
  getDashboardStats,
  getPatientAnalytics,
  getAppointmentAnalytics,
  getFinancialAnalytics,
  getLaboratoryAnalytics,
  getOperationalReports,
  getClinicalReports,
  getQualityReports,
  generateCustomReport,
  exportReport
} from '../controllers/reportController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Dashboard and analytics routes
router.get('/dashboard', getDashboardStats);
router.get('/patients', getPatientAnalytics);
router.get('/appointments', getAppointmentAnalytics);
router.get('/financial', getFinancialAnalytics);
router.get('/laboratory', getLaboratoryAnalytics);

// Comprehensive report routes
router.get('/operational', getOperationalReports);
router.get('/clinical', getClinicalReports);
router.get('/quality', getQualityReports);
router.post('/custom', generateCustomReport);

// Export routes
router.get('/:type/export', exportReport);

export default router;
