import React, { useState } from 'react';
import { 
  Users, 
  UserPlus, 
  Calendar, 
  Clock, 
  Award, 
  Search,
  Filter,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';

const staff = [
  {
    id: 'ST001',
    name: 'Dr. <PERSON>',
    role: 'Chief Medical Officer',
    department: 'Administration',
    specialization: 'Cardiology',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    joinDate: '2020-01-15',
    schedule: 'Day Shift',
    salary: 250000
  },
  {
    id: 'ST002',
    name: 'Dr. <PERSON>',
    role: 'Senior Surgeon',
    department: 'Surgery',
    specialization: 'Cardiac Surgery',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    joinDate: '2019-03-20',
    schedule: 'Day Shift',
    salary: 300000
  },
  {
    id: 'ST003',
    name: 'Nurse <PERSON>',
    role: 'Head Nurse',
    department: 'ICU',
    specialization: 'Critical Care',
    email: 'em<PERSON>.w<PERSON><PERSON>@hospital.com',
    phone: '+****************',
    status: 'Active',
    joinDate: '2018-07-10',
    schedule: 'Night Shift',
    salary: 75000
  },
  {
    id: 'ST004',
    name: 'Dr. <PERSON>',
    role: 'Pediatrician',
    department: 'Pediatrics',
    specialization: 'Child Medicine',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'On Leave',
    joinDate: '2021-02-01',
    schedule: 'Day Shift',
    salary: 180000
  }
];

const attendanceData = [
  { id: 'ST001', name: 'Dr. <PERSON> Johnson', present: 22, absent: 2, late: 1 },
  { id: 'ST002', name: 'Dr. Michael Smith', present: 21, absent: 1, late: 0 },
  { id: 'ST003', name: 'Nurse Emma Wilson', present: 23, absent: 1, late: 2 },
  { id: 'ST004', name: 'Dr. James Brown', present: 15, absent: 8, late: 0 }
];

const scheduleData = [
  { day: 'Monday', morning: 'Dr. Smith, Dr. Johnson', afternoon: 'Dr. Brown', night: 'Nurse Wilson' },
  { day: 'Tuesday', morning: 'Dr. Johnson, Dr. Brown', afternoon: 'Dr. Smith', night: 'Nurse Wilson' },
  { day: 'Wednesday', morning: 'Dr. Smith, Dr. Brown', afternoon: 'Dr. Johnson', night: 'Nurse Wilson' },
  { day: 'Thursday', morning: 'Dr. Johnson, Dr. Smith', afternoon: 'Dr. Brown', night: 'Nurse Wilson' },
  { day: 'Friday', morning: 'Dr. Brown, Dr. Johnson', afternoon: 'Dr. Smith', night: 'Nurse Wilson' }
];

export function HumanResources() {
  const [activeTab, setActiveTab] = useState('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'On Leave': return 'bg-yellow-100 text-yellow-800';
      case 'Inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredStaff = staff.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = departmentFilter === 'all' || member.department.toLowerCase() === departmentFilter.toLowerCase();
    
    return matchesSearch && matchesDepartment;
  });

  const departments = ['all', ...new Set(staff.map(member => member.department))];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Human Resources</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <UserPlus size={20} />
            <span>Add Staff</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Calendar size={20} />
            <span>Schedule</span>
          </button>
        </div>
      </div>

      {/* HR Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Staff</p>
              <p className="text-2xl font-bold text-gray-900">{staff.length}</p>
            </div>
            <Users size={24} className="text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Staff</p>
              <p className="text-2xl font-bold text-gray-900">
                {staff.filter(member => member.status === 'Active').length}
              </p>
            </div>
            <Users size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">On Leave</p>
              <p className="text-2xl font-bold text-gray-900">
                {staff.filter(member => member.status === 'On Leave').length}
              </p>
            </div>
            <Calendar size={24} className="text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Departments</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(staff.map(member => member.department)).size}
              </p>
            </div>
            <Award size={24} className="text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'staff'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Staff Directory
            </button>
            <button
              onClick={() => setActiveTab('attendance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'attendance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Attendance
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'schedule'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Schedule Management
            </button>
            <button
              onClick={() => setActiveTab('payroll')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'payroll'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Payroll
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'staff' && (
            <div className="space-y-6">
              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search staff by name, role, or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-center space-x-3">
                  <Filter size={20} className="text-gray-400" />
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {departments.map(dept => (
                      <option key={dept} value={dept}>
                        {dept === 'all' ? 'All Departments' : dept}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Staff Directory */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredStaff.map((member) => (
                  <div key={member.id} className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                        <Users size={24} className="text-white" />
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(member.status)}`}>
                        {member.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="font-medium text-gray-900">{member.name}</h3>
                      <p className="text-sm text-gray-600">{member.role}</p>
                      <p className="text-sm text-gray-500">{member.department}</p>
                      <p className="text-sm text-gray-500">{member.specialization}</p>
                      
                      <div className="pt-3 border-t border-gray-200">
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Mail size={14} />
                          <span>{member.email}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                          <Phone size={14} />
                          <span>{member.phone}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'attendance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Attendance Report</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Current Month</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Staff Member</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Present Days</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Absent Days</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Late Arrivals</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Attendance %</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {attendanceData.map((record) => {
                      const totalDays = record.present + record.absent;
                      const attendancePercentage = (record.present / totalDays) * 100;
                      
                      return (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="py-4 px-6 text-sm font-medium text-gray-900">{record.name}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.present}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.absent}</td>
                          <td className="py-4 px-6 text-sm text-gray-900">{record.late}</td>
                          <td className="py-4 px-6">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              attendancePercentage >= 90 ? 'bg-green-100 text-green-800' :
                              attendancePercentage >= 80 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {attendancePercentage.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'schedule' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Weekly Schedule</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock size={16} />
                  <span>Current Week</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Day</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Morning (6AM-2PM)</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Afternoon (2PM-10PM)</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Night (10PM-6AM)</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {scheduleData.map((schedule) => (
                      <tr key={schedule.day} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{schedule.day}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.morning}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.afternoon}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{schedule.night}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'payroll' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Payroll Management</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Current Month</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Staff Member</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Role</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Department</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Annual Salary</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {staff.map((member) => (
                      <tr key={member.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{member.name}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{member.role}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{member.department}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          ${member.salary.toLocaleString()}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(member.status)}`}>
                            {member.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}